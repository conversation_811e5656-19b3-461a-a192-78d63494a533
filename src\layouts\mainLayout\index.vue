<!--
 * @Description:
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2022-11-07 11:17:21
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-20 09:50:44
-->
<template>
  <div class="wrapper" id="wrapper">
    <div class="TopTitle">
      <div class="tabs">
        <div
          class="tab"
          :class="{ 'has-dropdown': item.name === '强补固拓' }"
          v-for="(item, i) in tab1"
          :key="i"
          @click="handleTabClick(item)"
          @mouseenter="item.name === '强补固拓' && showDropdown()"
          @mouseleave="item.name === '强补固拓' && hideDropdown()"
        >
          <!-- <div class="icon" :style="{backgroundImage:'url('+item.icon+')'}"></div> -->
          <div class="tabName">{{ item.name }}</div>
          <div v-if="item.name==='强补固拓' && showCylDropdown" class="cyl-dropdown" @click.stop>
            <div
              class="cyl-dropdown-item"
              :class="{ 'selected': selectedCylId === opt.id }"
              v-for="opt in cylOptions"
              :key="opt.id"
              @click.stop="selectCyl(opt.id)"
            >
              {{ opt.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="mainTitle">
        <div class="title">{{ titleName }}</div>
      </div>
      <div class="tabs" style="margin-left: 1500px">
        <div class="tab" v-for="(item, i) in tab2" :key="i" @click="handleTabClick(item)">
          <div class="tabName">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <router-view></router-view>
    <!-- <MapLayout ref="mapLayout" style="width: 7680px;height: 2160px;overflow: hidden"/> -->
    <!-- <weatherDialog v-show="dialogShow" @close="dialogShow = false" /> -->
  </div>
</template>

<script>
import MapLayout from '@/components/mapComponent/MapLayout.vue'
// import weatherDialog from '@/components/WeatherDialog'
import axios from 'axios'
import { getCsdnInterface } from '@/api/csdnIndexApi'

export default {
  components: {
    MapLayout,
    // weatherDialog,
  },
  data() {
    return {
      titleName: '工业智服',
      tab1: [
        { name: '工业智服', router: '/qyzf', icon: require('@/assets/common/topicon2.png') },
        { name: '强补固拓', router: '/qyzfCyl', icon: require('@/assets/common/topicon1.png') },
      ],
      tab2: [
        { name: '人口全域感知', router: '/rkhl', icon: require('@/assets/common/topicon4.png') },
        {
          name: '风险预警分析',
          router: '/fxyjfx',
          icon: require('@/assets/common/topicon5.png'),
        },
      ],
      showCylDropdown: false,
      selectedCylId: null, // 当前选中的产业链ID
      cylOptions: [
        { id: 104, name: '银杏' },
        { id: 103, name: '禽蛋', showTimeLine: 2 },
        // { id: 211, name: '电动工具及农机装备', showTimeLine: 2 },
        // { id: 102, name: '纺织服装', showTimeLine: 2 },
        // { id: 205, name: '智能家居', showTimeLine: 2 },
        // { id: 106, name: '生命健康', showTimeLine: 2 },
        // { id: 218, name: '新型电子材料', showTimeLine: 2 },
        // { id: 214, name: '集成电路及信创', showTimeLine: 2 },
        // { id: 216, name: '工业机床', showTimeLine: 2 },
        // { id: 217, name: '机器人', showTimeLine: 2 },
      ],

      time: '',
      date: '',
      timer: '',
      weather: {
        weatherIcon: '',
        temp: '',
        wind: '',
      },
      dialogShow: false,
      hideMapRoutes: ['/IndicatorCenter'],
    }
  },
  mounted() {
    this.startTime()
    this.date = this.getDate() + this.convertWeekday(new Date().getDay())
    // this.getWeather()
    this.$EventBus.$on('changeTopTitle', (e) => {
      this.titleName = e
    })
    this.updateSelectedCyl()
  },
  watch: {
    '$route'() {
      this.updateSelectedCyl()
    }
  },
  methods: {
    titleClick() {
      // this.$router.push('/qyzf')
    },
    pageJump(r) {
      this.$router.push(r)
    },
    handleTabClick(item) {
      this.titleName = item.name
      if (item.name === '强补固拓') {
        // 如果当前不在 /dyCyl 页面，则跳转到新能源汽车
       this.$router.push({ path: '/qyzfCyl' })
      } else {
        this.pageJump(item.router)
      }
    },
    showDropdown() {
      this.showCylDropdown = true
    },
    hideDropdown() {
      this.showCylDropdown = false
    },
    selectCyl(id) {
      this.showCylDropdown = false
      this.$router.push({ path: '/dyCyl', query: { id: String(id) } })
    },
    updateSelectedCyl() {
      // 如果当前在 dyCyl 页面，根据路由参数设置选中状态
      if (this.$route.path === '/dyCyl' && this.$route.query.id) {
        this.selectedCylId = parseInt(this.$route.query.id)
      } else {
        this.selectedCylId = null
      }
    },

    startTime() {
      this.timer = setInterval(() => {
        this.getTime()
      }, 1000)
    },
    getTime() {
      this.time = this.$moment(new Date().getTime()).format('HH:mm:ss')
    },
    getDate() {
      // 创建一个Date对象，通常不需要显式创建，因为当前时间会自动赋值
      var now = new Date()

      // 获取当前年份，JavaScript年份是从1900年开始的，所以需要加1900
      var year = now.getFullYear()

      // 获取当前月份，JavaScript的月份是从0开始的，所以需要加1
      var month = now.getMonth() + 1

      // 获取当前日期（1-31）
      var day = now.getDate()

      // 为了确保月份和日期是两位数，可以使用以下格式化方法
      if (month < 10) {
        month = '0' + month
      }
      if (day < 10) {
        day = '0' + day
      }

      // 现在你可以组合年月日
      var currentDate = year + '-' + month + '-' + day
      return currentDate
    },
    convertWeekday(num) {
      let weekday
      switch (num) {
        case 1:
          weekday = '星期一'
          break
        case 2:
          weekday = '星期二'
          break
        case 3:
          weekday = '星期三'
          break
        case 4:
          weekday = '星期四'
          break
        case 5:
          weekday = '星期五'
          break
        case 6:
          weekday = '星期六'
          break
        case 0:
          weekday = '星期日'
          break
        default:
          weekday = '输入错误'
      }
      return weekday
    },
    getWeather() {
      getCsdnInterface('cstz_tqjk', {}).then((res) => {
        let findObj = res.data.data.find((item) => item.ymd == this.getDate())
        let low = findObj.low.slice(2, findObj.low.length)
        let high = findObj.high.slice(2, findObj.high.length)
        this.weather.temp = low + '~' + high
        this.weather.wind = findObj.fx + ' ' + findObj.fl
        // this.weather.weatherIcon = require('@/assets/weather/' + findObj.weather_path.slice(25))
      })
    },
    WeatherClick() {
      this.dialogShow = true
    },
  },
}
</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  background-size: cover;
  .TopTitle {
    width: 7680px;
    height: 229px;
    background: url('../../assets/common/topbackground.png') no-repeat;
    background-size: cover;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    .tabs {
      width: 2993px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .tab {
        cursor: pointer;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        margin-left: 440px;
        .icon {
          width: 58px;
          height: 55px;
          background-size: cover;
        }
        .tabName {
          white-space: nowrap;
          font-size: 60px;
          font-family: FZZhengHeiS-DB-GB;
          font-weight: 400;
          color: #feffff;
          background: linear-gradient(
            180deg,
            #94aadb 1.8798828125%,
            #c3d8ee 50.244140625%,
            #ffffff 53.0029296875%,
            #ebf2ff 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-left: 17px;
        }
      }
      .has-dropdown {
        position: relative;
        padding-bottom: 10px;
      }
      .cyl-dropdown {
        position: absolute;
        top: 100px;
        left: 0;
        background: rgba(0, 0, 0, 0.7);
        border: 1px solid #5594c9;
        padding: 8px 0;
        min-width: 420px;
        z-index: 1000;
      }
      .cyl-dropdown-item {
        padding: 12px 20px;
        font-size: 36px;
        color: #fff;
        white-space: nowrap;
        cursor: pointer;
      }
      .cyl-dropdown-item + .cyl-dropdown-item {
        border-top: 1px solid rgba(255, 255, 255, 0.12);
      }
      .cyl-dropdown-item:hover {
        color: #c2e5ff;
      }
      .cyl-dropdown-item.selected {
        color: #5594c9;
        font-weight: bold;
      }
      .cyl-dropdown-item.selected:hover {
        color: #c2e5ff;
      }
    }
    .mainTitle {
      cursor: pointer;
      width: 1694px;
      height: 154px;
      // background: url('../../assets/common/topTextbg.png') no-repeat;
      background-size: cover;
      margin-top: -75px;
      text-align: center;
      position: absolute;
      left: 2993px;
      .title {
        margin-top: 15px;
        font-size: 80px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #ffffff;
        background: linear-gradient(0deg, #c2e5ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    // .weather {
    //   display: flex;
    //   justify-content: space-evenly;
    //   align-items: center;
    //   cursor: pointer;
    //   .weather-left {
    //     width: 270px;
    //     border-right: 3px solid #5594c9;
    //     .time {
    //       font-size: 50px;
    //       font-family: Source Han Sans CN;
    //       font-weight: 400;
    //       color: #fcfcfc;
    //     }
    //     .date {
    //       font-size: 28px;
    //       font-family: Source Han Sans CN;
    //       font-weight: 400;
    //       color: #83bbeb;
    //     }
    //   }
    //   .weather-right {
    //     margin: 0 25px 0 50px;
    //     .weatherTemp {
    //       display: flex;
    //       justify-content: space-evenly;
    //       align-items: center;
    //       img {
    //         width: 51px;
    //         height: 45px;
    //         margin-right: 11px;
    //       }
    //       .temp {
    //         font-size: 50px;
    //         font-family: Source Han Sans CN;
    //         font-weight: 400;
    //         color: #ffffff;
    //         white-space: nowrap;
    //       }
    //     }
    //     .wind {
    //       font-size: 28px;
    //       font-family: Source Han Sans CN;
    //       font-weight: 400;
    //       color: #83bbeb;
    //     }
    //   }
    // }
  }
}
</style>
