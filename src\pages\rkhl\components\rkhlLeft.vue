<template>
  <div class="rkhl-left">
    <!-- 人口实时监测 -->
    <div class="section population-monitor">
      <MainTitle title="人口实时监测" size="large" />
      <div class="section-content">
        <!-- 第一行 -->
        <div class="row">
          <PersonnelRanking />
          <HourlyTrend />
          <PopulationChangeChart />
        </div>
        <!-- 第二行 -->
        <div class="row">
          <PopulationStructure />
          <PopulationTrend />
          <PopulationFlow />
        </div>
      </div>
    </div>

    <!-- 重点区域监测 -->
    <div class="section area-monitor">
      <MainTitle title="重点区域监测" size="large">
        <template #right>
          <OptionsSwitcher v-model="selectedLevel" :options="sqList" @change="handleLevelChange" />
        </template>
      </MainTitle>
      <div class="section-content">
        <div class="row">
          <GovernmentFlow :current-level="selectedLevel" :sq-list="originalSqList" />
          <AreaPopulationChange :current-level="selectedLevel" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MainTitle from '@/components/MainTitle.vue'
import PersonnelRanking from './PersonnelRanking.vue'
import HourlyTrend from './HourlyTrend.vue'
import PopulationChangeChart from './PopulationChangeChart.vue'
import PopulationStructure from './PopulationStructure.vue'
import PopulationTrend from './PopulationTrend.vue'
import PopulationFlow from './PopulationFlow.vue'
import GovernmentFlow from './GovernmentFlow.vue'
import AreaPopulationChange from './AreaPopulationChange.vue'
import PopulationPortrait from './PopulationPortrait.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'RkhlLeft',
  components: {
    MainTitle,
    PersonnelRanking,
    HourlyTrend,
    PopulationChangeChart,
    PopulationStructure,
    PopulationTrend,
    PopulationFlow,
    GovernmentFlow,
    AreaPopulationChange,
    PopulationPortrait,
  },
  data() {
    return {
      selectedLevel: '重点场所',
      sqList: [
        { label: '重点场所', count: 2, value: '重点场所' },
        { label: '医疗保障机构', count: 2, value: '医疗保障机构' },
        { label: '活力商圈', count: 2, value: '活力商圈' },
        { label: '重点企业', count: 2, value: '重点企业' },
      ],
      originalSqList: [], // 保存原始API数据
    }
  },
  mounted() {
    this.initSqListApi()
  },
  methods: {
    // 初始化商圈分类API数据
    async initSqListApi() {
      try {
        const res = await getCsdnInterface1('csrk_zdqy_class', {})
        console.log('csrk_zdqy_class',res.data);
        this.originalSqList = res.data || []
        
        // 转换数据格式以适配 OptionsSwitcher 组件
        this.sqList = this.originalSqList.map(item => ({
          num: item.order_num,
          label: item.classification_name,
          value: item.classification_name,
          count: item.classification_count,
          classification_code: item.classification_code
        }))

        // 设置默认选中项
        if (this.sqList.length > 0) {
          this.selectedLevel = this.sqList[0].value
        }
      } catch (error) {
        console.error('获取商圈分类失败:', error)
      }
    },

    handleLevelChange(value) {
      this.selectedLevel = value
      const item = this.sqList.find(item => item.label === value)
      // 通过事件总线通知子组件更新数据
      this.$EventBus.$emit('levelChanged', item)
    },
  },
}
</script>

<style lang="less" scoped>
* {
  margin: 0;
  padding: 0;
}

.rkhl-left {
  width: 2350px;
  height: 1862px;
  padding: 10px 55px 0px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .section {
    width: 100%;
    margin-bottom: 40px;

    &.population-monitor {
      height: 66.67%; /* 2/3 的高度 */
    }

    &.area-monitor {
      height: 33.33%; /* 1/3 的高度 */
    }

    .section-content {
      width: 100%;
      height: calc(100% - 110px);
      position: relative;

      .row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        height: calc(50% - 10px);

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
